# RiceHuskInboundTaskMapper.xml 优化说明

## 优化背景

原有的 `getRiceHuskInboundTaskPageList` 方法存在以下问题：

1. **N+1 查询问题**：使用了 `collection` 标签的嵌套查询，每个主记录都会执行一次 `selectDetail` 查询，导致性能问题
2. **子表无数据时报错**：当使用 LEFT JOIN 时，如果子表没有数据，某些条件筛选会导致主表记录也被过滤掉

## 优化方案

### 1. 解决 N+1 查询问题

**优化前**：
```xml
<collection property="detailList"
            column="id"
            javaType="java.util.List" select="selectDetail">
</collection>
```

**优化后**：
```xml
<collection property="detailList" ofType="com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDetailDTO">
    <id column="detail_id" jdbcType="INTEGER" property="id" />
    <result column="detail_task_id" jdbcType="INTEGER" property="taskId" />
    <!-- 其他字段映射 -->
</collection>
```

通过在主查询中直接 JOIN 子表，一次性获取所有数据，避免了 N+1 查询问题。

### 2. 解决子表无数据时的报错问题

**核心思路**：根据筛选条件动态选择 JOIN 类型

- **有子表筛选条件时**：使用 `INNER JOIN`，确保只返回有子表数据的主表记录
- **无子表筛选条件时**：使用 `LEFT JOIN`，返回所有主表记录（包括没有子表数据的记录）

**实现代码**：
```xml
<choose>
    <!-- 当有子表相关的筛选条件时，使用INNER JOIN确保子表有数据 -->
    <when test="(siloCode != null and siloCode != '') or (status != null) or (orderDateStart != null) or (orderDateEnd != null)">
        INNER JOIN t_mpd_rice_husk_inbound_task_detail hikd ON hik.id = hikd.task_id AND hikd.deleted = 0
        <!-- 子表筛选条件 -->
    </when>
    <!-- 没有子表筛选条件时，使用LEFT JOIN显示所有主表记录 -->
    <otherwise>
        LEFT JOIN t_mpd_rice_husk_inbound_task_detail hikd ON hik.id = hikd.task_id AND hikd.deleted = 0
    </otherwise>
</choose>
```

## 筛选条件分类

### 子表筛选条件（使用 INNER JOIN）
- `siloCode`：入仓编码
- `status`：状态
- `orderDateStart`：开始日期
- `orderDateEnd`：结束日期

### 主表筛选条件（不影响 JOIN 类型）
- `orderNo`：工单号
- `materialCode`：物料编码（同时查询主表和子表）

## 性能优化效果

### 优化前
- **查询次数**：1 + N 次（N 为主表记录数）
- **问题**：当主表有 100 条记录时，总共执行 101 次 SQL 查询

### 优化后
- **查询次数**：1 次
- **效果**：无论主表有多少条记录，都只执行 1 次 SQL 查询

## 使用示例

### 1. 查询所有记录（包括没有子表数据的记录）
```java
Map<String, Object> params = new HashMap<>();
List<RiceHuskInboundTaskDTO> result = mapper.getRiceHuskInboundTaskPageList(params);
```

### 2. 按入仓编码筛选（只返回有对应子表数据的记录）
```java
Map<String, Object> params = new HashMap<>();
params.put("siloCode", "DK2-1");
List<RiceHuskInboundTaskDTO> result = mapper.getRiceHuskInboundTaskPageList(params);
```

### 3. 按物料编码筛选（主表或子表匹配即可）
```java
Map<String, Object> params = new HashMap<>();
params.put("materialCode", "11000544");
List<RiceHuskInboundTaskDTO> result = mapper.getRiceHuskInboundTaskPageList(params);
```

## 注意事项

1. **字段别名**：子表字段使用 `detail_` 前缀避免与主表字段冲突
2. **排序**：增加了 `hikd.actual_start_time DESC` 确保子表记录按时间排序
3. **兼容性**：保持了原有的接口和返回结果结构，无需修改调用代码

## 测试验证

已创建测试类 `RiceHuskInboundTaskMapperTest`，包含以下测试用例：

1. `testGetRiceHuskInboundTaskPageListWithoutConditions`：无筛选条件测试
2. `testGetRiceHuskInboundTaskPageListWithSiloCode`：入仓编码筛选测试
3. `testGetRiceHuskInboundTaskPageListWithStatus`：状态筛选测试
4. `testGetRiceHuskInboundTaskPageListWithMaterialCode`：物料编码筛选测试
5. `testGetRiceHuskInboundTaskPageListWithOrderNo`：工单号筛选测试
6. `testGetRiceHuskInboundTaskPageListWithMultipleConditions`：复合条件筛选测试

运行测试确保优化后的查询功能正常。
