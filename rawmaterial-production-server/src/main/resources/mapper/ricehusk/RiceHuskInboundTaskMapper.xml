<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.ricehusk.RiceHuskInboundTaskMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTask">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="silo_name" jdbcType="VARCHAR" property="siloName" />
        <result column="silo_code" jdbcType="VARCHAR" property="siloCode" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="quantity" jdbcType="DECIMAL" property="quantity" />
        <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
        <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
        <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
        <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id, order_no, order_date, silo_name, silo_code, material_name, material_code, quantity, stock_quantity, unit,
        start_time, end_time, status, remark, create_time, update_time, creator_id, updater_id, site_num, deleted
    </sql>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_rice_husk_inbound_task
        WHERE deleted = 0
        <if test="siloCode != null and siloCode != ''">
            AND silo_code = #{siloCode}
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND material_code = #{materialCode}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="orderDateStart != null">
            AND DATE(order_date) &gt;= DATE(#{orderDateStart})
        </if>
        <if test="orderDateEnd != null">
            AND DATE(order_date) &lt;= DATE(#{orderDateEnd})
        </if>
        ORDER BY order_date DESC, create_time DESC
    </select>

    <select id="selectMaxSerialNoByDate" resultType="java.lang.String">
        SELECT
            MAX(SUBSTRING(order_no, 9, 3)) AS max_serial_no
        FROM
            t_mpd_rice_husk_inbound_task
        WHERE
            deleted = 0
            AND order_no LIKE CONCAT('RC', DATE_FORMAT(#{orderDate}, '%y%m%d'), '%')
    </select>

    <select id="selectByOrderDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_rice_husk_inbound_task
        WHERE deleted = 0
            AND DATE(order_date) = DATE(#{orderDate})
        LIMIT 1
    </select>

    <select id="selectByDateAndMaterialCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_rice_husk_inbound_task
        WHERE deleted = 0
            AND DATE_FORMAT(order_date, '%Y%m%d') = #{dateStr}
            AND material_code = #{materialCode}
        LIMIT 1
    </select>


    <resultMap id="pageResultMap" type="com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDTO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="quantity" jdbcType="DECIMAL" property="quantity" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail"
        >
        </collection>
    </resultMap>

    <select id="getRiceHuskInboundTaskPageList" resultMap="pageResultMap">
        SELECT DISTINCT
        hik.id,hik.order_no,hik.order_date,hik.material_name,hik.material_code,
        hik.quantity,hik.unit,hik.remark,hik.create_time,hik.update_time
        FROM t_mpd_rice_husk_inbound_task hik
        LEFT JOIN t_mpd_rice_husk_inbound_task_detail hikd ON hik.id = hikd.task_id AND hikd.deleted = 0
        WHERE hik.deleted = 0
        <!-- 入仓编码：同时查询主表和子表 -->
        <if test="siloCode != null and siloCode != ''">
            AND hikd.silo_code = #{siloCode}
        </if>
        <!-- 物料编码：同时查询主表和子表 -->
        <if test="materialCode != null and materialCode != ''">
            AND (hik.material_code = #{materialCode} OR hikd.material_code = #{materialCode})
        </if>
        <!-- 状态：同时查询主表和子表 -->
        <if test="status != null">
            AND hikd.status = #{status}
        </if>
        <!-- 工单号：查询主表 -->
        <if test="orderNo != null and orderNo != ''">
            AND hik.order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <!-- 日期范围：同时查询主表工单日期和子表实际开始时间 -->
        <if test="orderDateStart != null">
            AND DATE(hikd.actual_start_time) &gt;= DATE(#{orderDateStart})
        </if>
        <if test="orderDateEnd != null">
            AND DATE(hikd.actual_start_time) &lt;= DATE(#{orderDateEnd})
        </if>
        ORDER BY hik.order_date DESC, hik.create_time DESC
    </select>

    <select id="selectDetail" resultType="com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDetailDTO">
        SELECT rhitd.id, rhitd.task_id, rhitd.silo_name, rhitd.silo_code, rhitd.material_name, rhitd.material_code,
               rhitd.unit, rhitd.quantity, rhitd.actual_start_time, rhitd.actual_completion_time, rhitd.status,
               rhitd.creator_name, rhitd.unique_id, rhitd.create_time, rhitd.update_time
        FROM t_mpd_rice_husk_inbound_task_detail rhitd
        WHERE rhitd.deleted = 0 AND rhitd.task_id = #{id}
        ORDER BY rhitd.actual_start_time DESC
    </select>
</mapper>
