package com.hvisions.rawmaterial;

import com.hvisions.brewage.client.RowClient;
import com.hvisions.brewage.dto.plan.dto.RowQueryReq;
import com.hvisions.brewage.dto.plan.vo.RowVO;
import com.hvisions.common.dto.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/28 16:25
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PurchaseProductionApplication.class)
public class YyyTest {
    @Resource
    private RowClient rowClient;

    @Test
    public void yyy() throws ParseException {
        RowQueryReq rowQueryReq = new RowQueryReq();
        PageInfo pageInfo = new PageInfo();
        rowQueryReq.setCheckState(true);
        rowQueryReq.setRowState("1");
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<RowVO> rowVOPage  = rowClient.queryPagedRow(rowQueryReq, pageInfo).getData();
        List<RowVO> records = rowVOPage.getRecords();
        System.out.println(records);


    }


}
